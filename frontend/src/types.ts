export interface Platform {
  id: number;
  name: string;
  identifier: string;
  description: string;
  is_active: boolean;
  created_at: string;
  updated_at: string;
}

export interface Architecture {
  id: number;
  name: string;
  identifier: string;
  description: string;
  is_active: boolean;
  created_at: string;
  updated_at: string;
}

// 发布版本类型
export interface Release {
  id: number;
  version: string;
  title: string;
  notes: string;
  platforms: Platform[];
  architectures: Architecture[];
  update_file: string;
  signature: string;
  is_active: boolean;
  is_prerelease: boolean;
  pub_date: string;
  created_at: string;
  updated_at: string;
}

// 更新检查记录类型
export interface UpdateCheck {
  id: number;
  client_ip: string;
  platform: string;
  architecture: string;
  current_version: string;
  user_agent: string;
  has_update: boolean;
  new_version: string;
  uuid: string;
  install_time: string;
  created_at: string;
}

// 下载记录类型
export interface DownloadRecord {
  id: number;
  release: Release;
  client_ip: string;
  platform: string;
  architecture: string;
  created_at: string;
}

// 表单数据类型
export interface PlatformFormData {
  name: string;
  identifier: string;
  description: string;
  is_active: boolean;
}

export interface ArchitectureFormData {
  name: string;
  identifier: string;
  description: string;
  is_active: boolean;
}

export interface ReleaseFormData {
  version: string;
  title: string;
  notes: string;
  platform_ids: number[];
  architecture_ids: number[];
  update_file: File | null;
  signature: string;
  is_active: boolean;
  is_prerelease: boolean;
  pub_date: string;
}

// 统计数据类型
export interface BasicStats {
  total_downloads: number;
  monthly_downloads: number;
  version_count: number;
  platform_count: number;
  architecture_count: number;
  total_update_checks: number;
}
