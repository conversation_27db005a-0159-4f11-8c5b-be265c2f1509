import React from 'react';
import { BrowserRouter as Router, Routes, Route } from 'react-router-dom';
import Layout from './components/Layout';
import Dashboard from './components/Dashboard';
import Platforms from './components/Platforms';
import Architectures from './components/Architectures';
import Releases from './components/Releases';
import UpdateChecks from './components/UpdateChecks';
import DownloadRecords from './components/DownloadRecords';

function App() {
  return (
    <Router>
      <Layout>
        <Routes>
          <Route path="/" element={<Dashboard />} />
          <Route path="/platforms" element={<Platforms />} />
          <Route path="/architectures" element={<Architectures />} />
          <Route path="/releases" element={<Releases />} />
          <Route path="/update-checks" element={<UpdateChecks />} />
          <Route path="/download-records" element={<DownloadRecords />} />
        </Routes>
      </Layout>
    </Router>
  );
}

export default App;
